import { FreebieService } from '../services/freebie.service';
import { FreebieProduct, FreebieCartItem } from '../models/freebie.model';
import { CartItem } from '../models/product.model';

export interface BillingTab {
  items: (CartItem | FreebieCartItem)[];
  freebiesProducts: FreebieProduct[];
  showFreebies: boolean;
  currentCartAmount: number;
  addedFreebies: Set<string>;
}

export class FreebieManager {
  constructor(private freebieService: FreebieService) {}

  async initializeFreebies(tab: BillingTab, cartAmount: number): Promise<void> {
    tab.currentCartAmount = cartAmount;
    await this.updateFreebies(tab);
  }

  async checkCartAmountAndUpdate(tab: BillingTab, newCartAmount: number): Promise<boolean> {
    if (newCartAmount === tab.currentCartAmount) return false;
    
    const previousCartAmount = tab.currentCartAmount;
    tab.currentCartAmount = newCartAmount;
    if (newCartAmount === 0) {
      this.clearFreebiesOnCartClear(tab);
    } else {
    // Check and remove any freebies that are no longer valid
      await this.removeInvalidFreebies(tab, previousCartAmount);
      await this.updateFreebies(tab);
    }
    return true;
  }

  clearFreebiesOnCartClear(tab: BillingTab): void {
    tab.items = this.freebieService.removeAllFreebies(tab.items);
    tab.addedFreebies.clear();
    tab.freebiesProducts = [];
    tab.showFreebies = false;
  }

  addFreebie(tab: BillingTab, freebie: FreebieProduct, validate: boolean = true): boolean {
    if (validate && !this.freebieService.canAddFreebie(tab.currentCartAmount, freebie, tab.items)) {
      return false;
    }
    
    // First, remove the freebie if it already exists
    const existingIndex = tab.items.findIndex(item => 
      'is_freebie' in item && item.is_freebie && item.freebie_id === freebie.id
    );
    
    if (existingIndex !== -1) {
      tab.items.splice(existingIndex, 1);
    }
    
    // Then add it at the end
    tab.items.push(this.freebieService.createFreebieCartItem(freebie));
    tab.addedFreebies.add(freebie.id);
    return true;
  }

  handleItemRemoval(tab: BillingTab, removedItem: CartItem | FreebieCartItem): void {
    if ('is_freebie' in removedItem && removedItem.is_freebie && removedItem.freebie_id) {
      tab.addedFreebies.delete(removedItem.freebie_id);
    }
  }

  getFreebieButtonState(tab: BillingTab, freebie: FreebieProduct) {
    const isInCart = this.freebieService.isFreebieInCart(tab.items, freebie.id);
    return {
      hidden: isInCart,
      disabled: !(tab.currentCartAmount >= freebie.amount) || isInCart
    };
  }

  private async updateFreebies(tab: BillingTab): Promise<void> {
    // Get all available freebies for the current cart amount
    const freebies = await this.freebieService.getAvailableFreebies(tab.currentCartAmount);
    
    // First, remove all existing freebies from the cart
    const nonFreebieItems = tab.items.filter(item => !('is_freebie' in item && item.is_freebie));
    
    // Clear the addedFreebies set since we're going to rebuild it
    tab.addedFreebies.clear();
    
    // Add back all non-freebie items first
    tab.items = [...nonFreebieItems];
    
    // Add all eligible freebie at the end
    freebies.forEach(freebie => {
      if (tab.currentCartAmount >= freebie.amount) {
        tab.items.push(this.freebieService.createFreebieCartItem(freebie));
        tab.addedFreebies.add(freebie.id);
      }
    });
    
    // Update the available freebies list in the tab
    tab.freebiesProducts = freebies;
    tab.showFreebies = freebies.length > 0;
  }

  private async removeInvalidFreebies(tab: BillingTab, previousCartAmount: number): Promise<void> {
    const freebiesToRemove = tab.items.filter((item): item is FreebieCartItem => 
      'is_freebie' in item && item.is_freebie && tab.currentCartAmount < (item.freebie_amount || 0)
    );

    if (freebiesToRemove.length > 0) {
      // Remove the invalid freebies from the cart
      tab.items = tab.items.filter(item => 
        !('is_freebie' in item) || !item.is_freebie || tab.currentCartAmount >= (item.freebie_amount || 0)
      );
      
      // Update the addedFreebies set
      freebiesToRemove.forEach(item => {
        if (item.freebie_id) {
          tab.addedFreebies.delete(item.freebie_id);
        }
      });
    }
  }
}