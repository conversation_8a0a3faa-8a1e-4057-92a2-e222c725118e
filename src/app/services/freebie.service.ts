import { Injectable } from '@angular/core';
import { FreebieProduct, FreebieCartItem } from '../models/freebie.model';
import { CartItem } from '../models/product.model';
import { TypeSenseService } from './typesense.service';

@Injectable({
  providedIn: 'root'
})
export class FreebieService {
  constructor(private typesenseService: TypeSenseService) {}

  /**
   * Automatically manage freebies in cart based on cart amount
   * This is the main method that should be called when cart changes
   */
  async autoManageFreebies(cartItems: CartItem[]): Promise<CartItem[]> {
    try {
      // Calculate cart amount excluding freebies to prevent recursive additions
      const cartAmount = this.calculateCartAmountExcludingFreebies(cartItems);

      // Get eligible freebies for current cart amount
      const eligibleFreebies = await this.getEligibleFreebies(cartAmount);

      // Remove all existing freebies from cart
      const nonFreebieItems = cartItems.filter((item: any) => !item.is_freebie);

      // Add eligible freebies to cart
      const updatedCartItems = [...nonFreebieItems];
      eligibleFreebies.forEach(freebie => {
        updatedCartItems.push(this.createFreebieCartItem(freebie));
      });

      return updatedCartItems;
    } catch (error) {
      console.error('Error auto-managing freebies:', error);
      return cartItems;
    }
  }

  /**
   * Calculate cart amount excluding freebie items
   */
  private calculateCartAmountExcludingFreebies(cartItems: CartItem[]): number {
    return cartItems
      .filter((item: any) => !item.is_freebie)
      .reduce((total, item) => total + (item.selling_price || 0) * (item.quantity || 0), 0);
  }

  /**
   * Get eligible freebies for the given cart amount
   */
  private async getEligibleFreebies(cartAmount: number): Promise<FreebieProduct[]> {
    try {
      const freebies = await this.typesenseService.getFreebiesProducts(cartAmount);
      // Return all eligible freebies, not just the highest one
      return freebies || [];
    } catch (error) {
      console.error('Error fetching eligible freebies:', error);
      return [];
    }
  }

  /**
   * Create freebie cart item from freebie product
   */
  private createFreebieCartItem(freebie: FreebieProduct): FreebieCartItem {
    return {
      id: `freebie_${freebie.id}`,
      name: freebie.name,
      child_sku: freebie.child_sku || freebie.sku,
      selling_price: 0,
      quantity: 1,
      tax: 0, cgst: 0, sgst: 0, igst: 0, cess: 0,
      taxable: false,
      thumbnail_image: freebie.thumbnail_image,
      variant_name: freebie.variant_name,
      is_freebie: true,
      freebie_id: freebie.id,
      freebie_amount: freebie.amount,
      freebie_name: freebie.name
    } as any;
  }

  /**
   * Check if a freebie is already in the cart
   */
  isFreebieInCart(cartItems: CartItem[], freebieId: string): boolean {
    return cartItems.some((item: any) => item.is_freebie && item.freebie_id === freebieId);
  }

  /**
   * Remove all freebies from cart
   */
  removeAllFreebies(cartItems: CartItem[]): CartItem[] {
    return cartItems.filter((item: any) => !item.is_freebie);
  }

  /**
   * Get cart amount excluding freebies (public method for external use)
   */
  getCartAmountExcludingFreebies(cartItems: CartItem[]): number {
    return this.calculateCartAmountExcludingFreebies(cartItems);
  }
}